import chalk from 'chalk';
import * as readline from 'readline';
import { ConfigManager } from '../core/config.js';
import { LLMManager } from '../core/llm-manager.js';
import { ModernSpinner } from './spinner.js';
import { Config, ProviderError } from '../types/index.js';

export interface OnboardingResult {
  config: Config;
  success: boolean;
  message: string;
  autoStart?: boolean;
}

export class OnboardingFlow {
  private configManager: ConfigManager;
  private llmManager: LLMManager;
  private rl: readline.Interface;

  constructor() {
    this.configManager = ConfigManager.getInstance();
    this.llmManager = new LLMManager();
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      terminal: true
    });
  }

  private async simplePrompt(question: string): Promise<string> {
    return new Promise((resolve) => {
      // Make sure stdin is in the right mode
      if (process.stdin.isTTY) {
        process.stdin.setRawMode(false);
      }

      this.rl.question(question, (answer) => {
        resolve(answer.trim());
      });
    });
  }

  private async simpleChoice(question: string, choices: string[]): Promise<number> {
    console.log(question);
    choices.forEach((choice, index) => {
      console.log(`  ${index + 1}. ${choice}`);
    });

    while (true) {
      const answer = await this.simplePrompt('\nEnter your choice (number): ');

      if (!answer) {
        console.log(chalk.yellow('No input received. Please try again.'));
        continue;
      }

      const choice = parseInt(answer);
      if (choice >= 1 && choice <= choices.length) {
        return choice - 1;
      }
      console.log(chalk.red('Invalid choice. Please try again.'));
    }
  }

  private cleanup() {
    this.rl.close();
  }

  public async start(): Promise<OnboardingResult> {
    // Stop any running spinners before starting
    ModernSpinner.stopAll();

    console.clear();
    this.showWelcome();

    try {
      // Check if already configured
      if (this.configManager.isConfigured()) {
        const shouldReconfigure = await this.askReconfigure();
        if (!shouldReconfigure) {
          return {
            config: this.configManager.getAll(),
            success: true,
            message: 'Using existing configuration'
          };
        }
      }

      // Start configuration flow
      const provider = await this.selectProvider();
      const config = await this.configureProvider(provider);

      // Test connection
      await this.testConnection(config);

      // Save configuration
      this.configManager.setAll(config);

      console.log(chalk.green('\n✅ Configuration completed successfully!'));

      // Ask if user wants to start interactive mode
      console.log(chalk.gray('\n🔄 Asking user about auto-start...'));
      const shouldStart = await this.askAutoStart();
      console.log(chalk.gray(`✅ User choice: ${shouldStart ? 'Start interactive mode' : 'Exit without starting'}`));

      if (shouldStart) {
        console.log(chalk.cyan('\n🚀 Starting interactive mode...\n'));
        return {
          config: config as Config,
          success: true,
          message: 'Configuration completed successfully',
          autoStart: true
        };
      } else {
        console.log(chalk.cyan('You can start the interactive mode anytime with: arien\n'));
        return {
          config: config as Config,
          success: true,
          message: 'Configuration completed successfully',
          autoStart: false
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.log(chalk.red(`\n❌ Configuration failed: ${errorMessage}\n`));

      return {
        config: this.configManager.getAll(),
        success: false,
        message: errorMessage
      };
    } finally {
      this.cleanup();
    }
  }

  private showWelcome(): void {
    console.log(chalk.cyan.bold('\n🚀 Welcome to Arien AI CLI!\n'));
    console.log(chalk.white('This powerful CLI tool allows you to interact with AI models'));
    console.log(chalk.white('and execute shell commands through natural language.\n'));
    console.log(chalk.yellow('Let\'s get you set up!\n'));
  }

  private async askReconfigure(): Promise<boolean> {
    // Ensure no spinners are running
    ModernSpinner.stopAll();

    const answer = await this.simplePrompt('Configuration already exists. Do you want to reconfigure? (y/N): ');
    return answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
  }

  private async askAutoStart(): Promise<boolean> {
    // Ensure no spinners are running
    ModernSpinner.stopAll();

    console.log(chalk.cyan('\n🚀 Setup Complete!'));
    console.log(chalk.gray('The interactive terminal allows you to chat with AI and execute commands.'));

    // Check for environment variable to skip prompt (for testing)
    if (process.env.ARIEN_AUTO_START !== undefined) {
      const autoStart = process.env.ARIEN_AUTO_START.toLowerCase() === 'true';
      console.log(chalk.gray(`🔧 Using environment variable ARIEN_AUTO_START=${autoStart}`));
      return autoStart;
    }

    try {
      const answer = await this.simplePrompt('\nWould you like to start the interactive terminal now? (Y/n): ');
      const response = answer.toLowerCase().trim();

      // If empty response (timeout or just Enter), default to yes
      if (!response) {
        console.log(chalk.green('✅ No input received, starting interactive mode by default...'));
        return true;
      }

      return response !== 'n' && response !== 'no';
    } catch (error) {
      console.log(chalk.yellow('\n⚠️  Prompt failed. Starting interactive mode by default.'));
      return true; // Default to starting if there's an issue
    }
  }

  private async selectProvider(): Promise<'deepseek' | 'ollama'> {
    console.log(chalk.blue.bold('\n📡 Select your AI Provider:\n'));

    // Ensure no spinners are running before showing prompt
    ModernSpinner.stopAll();

    const choices = [
      `${chalk.green('Deepseek')} - Cloud-based AI with models like deepseek-chat and deepseek-reasoner`,
      `${chalk.blue('Ollama')} - Local AI models running on your machine`
    ];

    const choice = await this.simpleChoice('Choose your preferred AI provider:', choices);

    return choice === 0 ? 'deepseek' : 'ollama';
  }

  private async configureProvider(provider: 'deepseek' | 'ollama'): Promise<Partial<Config>> {
    if (provider === 'deepseek') {
      return await this.configureDeepseek();
    } else {
      return await this.configureOllama();
    }
  }

  private async configureDeepseek(): Promise<Partial<Config>> {
    console.log(chalk.blue.bold('\n🔑 Configure Deepseek:\n'));
    console.log(chalk.gray('You can get your API key from: https://platform.deepseek.com/api_keys\n'));

    // Ensure no spinners are running
    ModernSpinner.stopAll();

    // Get API key
    let apiKey = '';
    while (!apiKey) {
      apiKey = await this.simplePrompt('Enter your Deepseek API key: ');
      if (!apiKey.trim()) {
        console.log(chalk.red('API key is required'));
        continue;
      }
      if (!apiKey.startsWith('sk-')) {
        console.log(chalk.yellow('Warning: Deepseek API keys typically start with "sk-"'));
      }
      break;
    }

    // Select model
    const modelChoices = [
      'deepseek-chat (Recommended for general use)',
      'deepseek-reasoner (Better for complex reasoning)'
    ];
    const modelChoice = await this.simpleChoice('Select a model:', modelChoices);
    const model = modelChoice === 0 ? 'deepseek-chat' : 'deepseek-reasoner';

    // Get base URL
    const baseUrlInput = await this.simplePrompt('Base URL (press Enter for default): ');
    const baseUrl = baseUrlInput.trim() || 'https://api.deepseek.com/v1';

    // Get max tokens
    let maxTokens = 4096;
    const maxTokensInput = await this.simplePrompt('Maximum tokens per response (default 4096): ');
    if (maxTokensInput.trim()) {
      const parsed = parseInt(maxTokensInput);
      if (parsed > 0 && parsed <= 32768) {
        maxTokens = parsed;
      }
    }

    // Get temperature
    let temperature = 0.7;
    const temperatureInput = await this.simplePrompt('Temperature 0.0-2.0 (default 0.7): ');
    if (temperatureInput.trim()) {
      const parsed = parseFloat(temperatureInput);
      if (parsed >= 0 && parsed <= 2) {
        temperature = parsed;
      }
    }

    return {
      provider: 'deepseek',
      apiKey,
      model,
      baseUrl,
      maxTokens,
      temperature,
      workingDirectory: process.cwd(),
      autoApprove: false,
      retryAttempts: 3,
      timeout: 30000
    };
  }

  private async configureOllama(): Promise<Partial<Config>> {
    console.log(chalk.blue.bold('\n🏠 Configure Ollama:\n'));
    console.log(chalk.gray('Make sure Ollama is installed and running on your system.\n'));
    console.log(chalk.gray('Install Ollama from: https://ollama.ai\n'));

    // Ensure no spinners are running
    ModernSpinner.stopAll();

    // Get base URL
    const baseUrlInput = await this.simplePrompt('Ollama server URL (default http://localhost:11434): ');
    const baseUrl = baseUrlInput.trim() || 'http://localhost:11434';

    // Get model name
    let model = '';
    while (!model) {
      model = await this.simplePrompt('Model name (e.g., llama2, codellama, mistral): ');
      if (!model.trim()) {
        console.log(chalk.red('Model name is required'));
        model = '';
      }
    }

    // Get max tokens
    let maxTokens = 4096;
    const maxTokensInput = await this.simplePrompt('Maximum tokens per response (default 4096): ');
    if (maxTokensInput.trim()) {
      const parsed = parseInt(maxTokensInput);
      if (parsed > 0 && parsed <= 32768) {
        maxTokens = parsed;
      }
    }

    // Get temperature
    let temperature = 0.7;
    const temperatureInput = await this.simplePrompt('Temperature 0.0-2.0 (default 0.7): ');
    if (temperatureInput.trim()) {
      const parsed = parseFloat(temperatureInput);
      if (parsed >= 0 && parsed <= 2) {
        temperature = parsed;
      }
    }

    return {
      provider: 'ollama',
      baseUrl,
      model,
      maxTokens,
      temperature,
      workingDirectory: process.cwd(),
      autoApprove: false,
      retryAttempts: 3,
      timeout: 60000 // Longer timeout for local models
    };
  }

  private async testConnection(config: Partial<Config>): Promise<void> {
    const spinner = ModernSpinner.createConnectingSpinner('Testing connection...');
    
    try {
      spinner.start();
      
      // Temporarily set config for testing
      const originalConfig = this.configManager.getAll();
      this.configManager.setAll(config);
      
      // Initialize and test provider
      await this.llmManager.initializeProvider();
      const isConnected = await this.llmManager.validateConnection();
      
      if (!isConnected) {
        throw new ProviderError('Failed to connect to the provider');
      }

      // Test getting available models
      const models = await this.llmManager.getAvailableModels();
      
      spinner.succeed('Connection successful!');
      
      if (models.length > 0) {
        console.log(chalk.green(`✅ Found ${models.length} available models`));
      }
      
      // Restore original config
      this.configManager.setAll(originalConfig);
      
    } catch (error) {
      spinner.fail('Connection failed');
      throw error;
    }
  }

  public async quickSetup(): Promise<OnboardingResult> {
    try {
      // Quick setup with defaults for development/testing
      const config: Partial<Config> = {
        provider: 'deepseek',
        model: 'deepseek-chat',
        baseUrl: 'https://api.deepseek.com/v1',
        maxTokens: 4096,
        temperature: 0.7,
        workingDirectory: process.cwd(),
        autoApprove: false,
        retryAttempts: 3,
        timeout: 30000
      };

      // Ask for API key only
      // Ensure no spinners are running
      ModernSpinner.stopAll();

      let apiKey = '';
      while (!apiKey) {
        apiKey = await this.simplePrompt('Enter your Deepseek API key for quick setup: ');
        if (!apiKey.trim()) {
          console.log(chalk.red('API key is required'));
        }
      }

      config.apiKey = apiKey;

      try {
        await this.testConnection(config);
        this.configManager.setAll(config);

        return {
          config: config as Config,
          success: true,
          message: 'Quick setup completed successfully'
        };
      } catch (error) {
        return {
          config: this.configManager.getAll(),
          success: false,
          message: error instanceof Error ? error.message : 'Quick setup failed'
        };
      }
    } finally {
      this.cleanup();
    }
  }
}
