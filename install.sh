#!/bin/bash

# Arien AI CLI Installation Script
# Supports Windows WSL, macOS, and Linux
# Usage: ./install.sh [install|update|uninstall]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PACKAGE_NAME="arien-ai-cli"
BINARY_NAME="arien"
INSTALL_DIR="$HOME/.local/bin"
CONFIG_DIR="$HOME/.config/arien-ai-cli"
REPO_URL="https://github.com/arien-ai/arien-ai-cli.git"
NPM_PACKAGE="arien-ai-cli"

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if grep -q Microsoft /proc/version 2>/dev/null; then
            OS="wsl"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]]; then
        OS="windows"
    else
        OS="unknown"
    fi
}

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                     Arien AI CLI Installer                  ║"
    echo "║          Modern CLI with LLM Integration & Function Calling ║"
    echo "║                                                              ║"
    echo "║  🚀 Features:                                                ║"
    echo "║    • LLM Integration (Deepseek & Ollama)                    ║"
    echo "║    • Function Calling & Shell Command Execution            ║"
    echo "║    • Session Management & Message History                   ║"
    echo "║    • Modern Terminal UI with Real-time Updates             ║"
    echo "║    • Cross-Platform Support (Windows WSL, macOS, Linux)    ║"
    echo "║    • Intelligent Error Handling & Retry Logic              ║"
    echo "║    • Enhanced Tool Descriptions & Parallel Execution       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if Arien AI CLI is already installed
check_existing_installation() {
    if command_exists "$BINARY_NAME"; then
        # Try to get version from package.json in npm global directory
        if check_npm_installation; then
            EXISTING_VERSION=$(npm list -g "$NPM_PACKAGE" 2>/dev/null | grep "$NPM_PACKAGE@" | sed 's/.*@\([0-9]\+\.[0-9]\+\.[0-9]\+\).*/v\1/' || echo "unknown")
        else
            # Fallback to checking if binary exists
            EXISTING_VERSION="installed"
        fi
        return 0
    else
        return 1
    fi
}

# Get current package version
get_package_version() {
    if [ -f "package.json" ]; then
        grep '"version"' package.json | sed 's/.*"version": *"\([^"]*\)".*/\1/'
    else
        echo "unknown"
    fi
}

# Check if npm global package exists
check_npm_installation() {
    npm list -g "$NPM_PACKAGE" >/dev/null 2>&1
}

# Prompt user for installation choice
prompt_installation_choice() {
    local current_version="$1"
    local new_version="$2"

    echo ""
    print_status "🔍 Existing installation detected!"
    echo ""
    echo "Current version: $current_version"
    echo "Available version: $new_version"
    echo ""
    echo "What would you like to do?"
    echo "  1. Update existing installation"
    echo "  2. Reinstall completely (clean install)"
    echo "  3. Cancel installation"
    echo ""
    echo -n "Enter your choice (1-3): "
    read -r choice

    case "$choice" in
        1)
            return 1  # Update
            ;;
        2)
            return 2  # Reinstall
            ;;
        3)
            return 3  # Cancel
            ;;
        *)
            print_warning "Invalid choice. Defaulting to update."
            return 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 20+ first."
        print_status "Visit: https://nodejs.org/"
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)
    
    if [ "$MAJOR_VERSION" -lt 20 ]; then
        print_error "Node.js version 20+ is required. Current version: $NODE_VERSION"
        exit 1
    fi
    
    print_success "Node.js $NODE_VERSION detected"
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "npm $(npm --version) detected"
    
    # Check git (optional)
    if ! command_exists git; then
        print_warning "Git is not installed. Some features may not work properly."
    fi
}

# Create directories
create_directories() {
    print_status "Creating directories..."
    
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$CONFIG_DIR"
    
    print_success "Directories created"
}

# Add to PATH
add_to_path() {
    print_status "Adding to PATH..."
    
    # Determine shell config file
    if [ -n "$ZSH_VERSION" ]; then
        SHELL_CONFIG="$HOME/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        if [ -f "$HOME/.bashrc" ]; then
            SHELL_CONFIG="$HOME/.bashrc"
        else
            SHELL_CONFIG="$HOME/.bash_profile"
        fi
    else
        SHELL_CONFIG="$HOME/.profile"
    fi
    
    # Check if already in PATH
    if echo "$PATH" | grep -q "$INSTALL_DIR"; then
        print_success "Already in PATH"
        return
    fi
    
    # Add to shell config
    if [ -f "$SHELL_CONFIG" ]; then
        if ! grep -q "$INSTALL_DIR" "$SHELL_CONFIG"; then
            echo "" >> "$SHELL_CONFIG"
            echo "# Arien AI CLI" >> "$SHELL_CONFIG"
            echo "export PATH=\"$INSTALL_DIR:\$PATH\"" >> "$SHELL_CONFIG"
            print_success "Added to PATH in $SHELL_CONFIG"
        else
            print_success "Already configured in $SHELL_CONFIG"
        fi
    else
        print_warning "Could not find shell config file. Please add $INSTALL_DIR to your PATH manually."
    fi
}

# Install via npm
install_npm() {
    print_status "Installing via npm..."
    
    if npm list -g "$NPM_PACKAGE" >/dev/null 2>&1; then
        print_status "Package already installed. Updating..."
        npm update -g "$NPM_PACKAGE"
    else
        npm install -g "$NPM_PACKAGE"
    fi
    
    print_success "Installed via npm"
}

# Install from source
install_source() {
    print_status "Installing from source..."

    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"

    print_status "Cloning repository..."
    git clone "$REPO_URL" .

    print_status "Installing dependencies..."
    npm install

    print_status "Building project..."
    npm run build

    print_status "Creating global link..."
    npm link

    cd - >/dev/null
    rm -rf "$TEMP_DIR"

    print_success "Installed from source"
}

# Install from local directory
install_local() {
    print_status "Installing from local directory..."

    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Please run this script from the project root directory."
        exit 1
    fi

    # Verify this is the Arien AI CLI project
    if ! grep -q "arien-ai-cli" package.json; then
        print_error "This doesn't appear to be the Arien AI CLI project directory."
        exit 1
    fi

    print_status "Installing dependencies..."
    npm install

    print_status "Building project..."
    npm run build

    print_status "Creating global link..."
    npm link

    print_success "Installed from local directory"
}

# Update existing installation
update_existing_installation() {
    print_status "Updating existing installation..."

    # Check if installed via npm
    if check_npm_installation; then
        print_status "Updating via npm..."
        npm update -g "$NPM_PACKAGE"
    else
        print_status "Updating from source..."
        install_source
    fi

    print_success "Update completed!"
    print_status "🎉 Arien AI CLI has been updated!"
}

# Update local installation
update_local_installation() {
    print_status "Updating from local directory..."

    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Please run this script from the project root directory."
        exit 1
    fi

    # Verify this is the Arien AI CLI project
    if ! grep -q "arien-ai-cli" package.json; then
        print_error "This doesn't appear to be the Arien AI CLI project directory."
        exit 1
    fi

    print_status "Installing dependencies..."
    npm install

    print_status "Building project..."
    npm run build

    print_status "Updating global link..."
    npm link

    print_success "Local update completed!"
    print_status "🎉 Arien AI CLI has been updated from local directory!"
}

# Silent uninstall (no prompts)
uninstall_silent() {
    print_status "Removing existing installation..."

    # Remove npm package
    if check_npm_installation; then
        npm uninstall -g "$NPM_PACKAGE" >/dev/null 2>&1
        print_success "Removed npm package"
    fi

    # Remove binary if exists
    if [ -f "$INSTALL_DIR/$BINARY_NAME" ]; then
        rm -f "$INSTALL_DIR/$BINARY_NAME"
        print_success "Removed binary"
    fi

    print_success "Previous installation removed"
}

# Post-installation setup
post_install_setup() {
    print_status "Setting up Arien AI CLI..."

    # Create desktop shortcut (Linux/WSL)
    if [[ "$OS" == "linux" || "$OS" == "wsl" ]]; then
        create_desktop_shortcut
    fi

    # Check for common AI tools
    check_ai_tools

    # Create sample configuration
    create_sample_config
}

# Create desktop shortcut
create_desktop_shortcut() {
    DESKTOP_DIR="$HOME/Desktop"
    if [ -d "$DESKTOP_DIR" ]; then
        cat > "$DESKTOP_DIR/Arien-AI-CLI.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Arien AI CLI
Comment=Modern CLI with LLM Integration & Function Calling
Exec=gnome-terminal -- arien
Icon=terminal
Terminal=false
Categories=Development;Utility;
EOF
        chmod +x "$DESKTOP_DIR/Arien-AI-CLI.desktop"
        print_success "Desktop shortcut created"
    fi
}

# Check for AI tools
check_ai_tools() {
    print_status "Checking for AI tools..."

    # Check for Ollama
    if command_exists ollama; then
        print_success "Ollama detected - you can use local AI models"
    else
        print_warning "Ollama not found - install from https://ollama.ai for local AI models"
    fi

    # Check for common development tools
    if command_exists git; then
        print_success "Git detected"
    else
        print_warning "Git not found - some features may be limited"
    fi

    if command_exists python3 || command_exists python; then
        print_success "Python detected"
    fi

    if command_exists docker; then
        print_success "Docker detected"
    fi
}

# Create sample configuration
create_sample_config() {
    SAMPLE_CONFIG="$CONFIG_DIR/sample-config.json"

    cat > "$SAMPLE_CONFIG" << EOF
{
  "provider": "deepseek",
  "model": "deepseek-chat",
  "baseUrl": "https://api.deepseek.com/v1",
  "maxTokens": 4096,
  "temperature": 0.7,
  "workingDirectory": "$HOME",
  "autoApprove": false,
  "retryAttempts": 3,
  "timeout": 30000
}
EOF

    print_success "Sample configuration created at $SAMPLE_CONFIG"
}

# Install function
install() {
    print_header
    print_status "Starting installation for $OS..."

    check_prerequisites

    # Check for existing installation
    if check_existing_installation; then
        current_version="$EXISTING_VERSION"
        new_version="latest"

        prompt_installation_choice "$current_version" "$new_version"
        choice_result=$?

        case $choice_result in
            1)  # Update
                print_status "Updating existing installation..."
                update_existing_installation
                return
                ;;
            2)  # Reinstall
                print_status "Performing clean reinstallation..."
                uninstall_silent
                ;;
            3)  # Cancel
                print_status "Installation cancelled by user."
                exit 0
                ;;
        esac
    fi

    create_directories

    # Try npm first, fallback to source
    if command_exists npm; then
        if npm view "$NPM_PACKAGE" >/dev/null 2>&1; then
            install_npm
        else
            print_warning "Package not found on npm. Installing from source..."
            install_source
        fi
    else
        install_source
    fi

    add_to_path

    print_success "Installation completed!"

    # Post-installation setup
    post_install_setup

    print_status "🎉 Arien AI CLI is now installed!"
    print_status ""
    print_status "Next steps:"
    print_status "1. Restart your terminal or run: source ~/.bashrc"
    print_status "2. Run 'arien config' to set up your AI provider"
    print_status "3. Run 'arien' to start the interactive terminal"
    print_status ""
    print_status "For help: arien --help"
    print_status "Documentation: https://github.com/arien-ai/arien-ai-cli#readme"
}

# Install from local directory function
install_local_cmd() {
    print_header
    print_status "Starting local installation for $OS..."

    check_prerequisites

    # Check for existing installation
    if check_existing_installation; then
        current_version="$EXISTING_VERSION"
        new_version=$(get_package_version)

        prompt_installation_choice "$current_version" "$new_version"
        choice_result=$?

        case $choice_result in
            1)  # Update
                print_status "Updating existing installation from local directory..."
                update_local_installation
                return
                ;;
            2)  # Reinstall
                print_status "Performing clean reinstallation from local directory..."
                uninstall_silent
                ;;
            3)  # Cancel
                print_status "Installation cancelled by user."
                exit 0
                ;;
        esac
    fi

    create_directories

    install_local
    add_to_path

    print_success "Local installation completed!"

    # Post-installation setup
    post_install_setup

    print_status "🎉 Arien AI CLI is now installed from local directory!"
    print_status ""
    print_status "Next steps:"
    print_status "1. Restart your terminal or run: source ~/.bashrc"
    print_status "2. Run 'arien config' to set up your AI provider"
    print_status "3. Run 'arien' to start the interactive terminal"
    print_status ""
    print_status "For help: arien --help"
    print_status "Documentation: https://github.com/arien-ai/arien-ai-cli#readme"
}

# Update function
update() {
    print_header
    print_status "Updating Arien AI CLI..."
    
    if command_exists "$BINARY_NAME"; then
        # Check if installed via npm
        if npm list -g "$NPM_PACKAGE" >/dev/null 2>&1; then
            print_status "Updating via npm..."
            npm update -g "$NPM_PACKAGE"
        else
            print_status "Updating from source..."
            install_source
        fi
        
        print_success "Update completed!"
    else
        print_error "Arien AI CLI is not installed. Run './install.sh install' first."
        exit 1
    fi
}

# Uninstall function
uninstall() {
    print_header
    print_status "Uninstalling Arien AI CLI..."
    
    # Remove npm package
    if npm list -g "$NPM_PACKAGE" >/dev/null 2>&1; then
        npm uninstall -g "$NPM_PACKAGE"
        print_success "Removed npm package"
    fi
    
    # Remove binary if exists
    if [ -f "$INSTALL_DIR/$BINARY_NAME" ]; then
        rm -f "$INSTALL_DIR/$BINARY_NAME"
        print_success "Removed binary"
    fi
    
    # Ask about config removal
    echo -n "Remove configuration files? [y/N]: "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm -rf "$CONFIG_DIR"
        print_success "Removed configuration files"
    fi
    
    # Remove from shell config
    for config in "$HOME/.bashrc" "$HOME/.bash_profile" "$HOME/.zshrc" "$HOME/.profile"; do
        if [ -f "$config" ] && grep -q "Arien AI CLI" "$config"; then
            # Create backup
            cp "$config" "$config.backup"
            # Remove lines
            sed -i '/# Arien AI CLI/,+1d' "$config" 2>/dev/null || true
            print_success "Removed from $config"
        fi
    done
    
    print_success "Uninstallation completed!"
}

# Show usage
show_usage() {
    echo "Usage: $0 [install|install-local|update|uninstall|help]"
    echo ""
    echo "🚀 Arien AI CLI Installation Script"
    echo "   Modern CLI with LLM Integration & Function Calling"
    echo ""
    echo "Commands:"
    echo "  install        Install Arien AI CLI with full setup (from npm/git)"
    echo "                 • Auto-detects existing installations"
    echo "                 • Offers update/reinstall options"
    echo "  install-local  Install from current local directory"
    echo "                 • Auto-detects existing installations"
    echo "                 • Compares local vs installed versions"
    echo "  update         Update existing installation to latest version"
    echo "  uninstall      Remove Arien AI CLI and optionally clean config"
    echo "  help           Show detailed help and system requirements"
    echo ""
    echo "🔍 Smart Installation Features:"
    echo "  • Automatic detection of existing installations"
    echo "  • Version comparison and upgrade prompts"
    echo "  • Choice between update or clean reinstall"
    echo "  • Preserves configuration during updates"
    echo ""
    echo "Examples:"
    echo "  $0 install                    # Smart install with auto-detection"
    echo "  $0 install-local              # Smart local install with version check"
    echo "  $0 update                     # Update to latest version"
    echo "  $0 uninstall                  # Remove installation"
    echo ""
    echo "Quick Install:"
    echo "  curl -fsSL https://raw.githubusercontent.com/arien-ai/arien-ai-cli/main/install.sh | bash"
    echo ""
    echo "Local Install (from project directory):"
    echo "  cd /path/to/arien-ai-cli && ./install.sh install-local"
    echo ""
    echo "System Requirements:"
    echo "  • Node.js 20+ (https://nodejs.org/)"
    echo "  • npm (included with Node.js)"
    echo "  • Git (recommended)"
    echo "  • 50MB free disk space"
    echo ""
    echo "Supported Platforms:"
    echo "  • Linux (Ubuntu, Debian, CentOS, etc.)"
    echo "  • macOS (Intel & Apple Silicon)"
    echo "  • Windows WSL (Windows Subsystem for Linux)"
    echo ""
    echo "For more information:"
    echo "  https://github.com/arien-ai/arien-ai-cli"
}

# Show detailed help
show_help() {
    print_header
    echo ""
    echo "🔧 SYSTEM REQUIREMENTS"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "• Node.js 20.0.0 or higher"
    echo "• npm (Node Package Manager)"
    echo "• Git (recommended for updates)"
    echo "• Terminal with color support"
    echo "• Internet connection for AI providers"
    echo ""
    echo "📦 INSTALLATION METHODS"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "1. Quick Install (Recommended):"
    echo "   curl -fsSL https://raw.githubusercontent.com/arien-ai/arien-ai-cli/main/install.sh | bash"
    echo ""
    echo "2. Manual Install:"
    echo "   git clone https://github.com/arien-ai/arien-ai-cli.git"
    echo "   cd arien-ai-cli"
    echo "   ./install.sh install"
    echo ""
    echo "3. Local Install (from source directory):"
    echo "   cd /path/to/arien-ai-cli"
    echo "   ./install.sh install-local"
    echo ""
    echo "4. NPM Install (if published):"
    echo "   npm install -g arien-ai-cli"
    echo ""
    echo "🔍 SMART INSTALLATION FEATURES"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "• Automatic detection of existing installations"
    echo "• Version comparison and upgrade recommendations"
    echo "• Interactive choice between update or clean reinstall"
    echo "• Configuration preservation during updates"
    echo "• Support for both npm and local source installations"
    echo ""
    echo "🚀 FEATURES"
    echo "━━━━━━━━━━━"
    echo "• LLM Integration with Deepseek and Ollama"
    echo "• Function Calling for shell command execution"
    echo "• Session management with persistent history"
    echo "• Modern terminal UI with real-time updates"
    echo "• Cross-platform support (Windows WSL, macOS, Linux)"
    echo "• Intelligent error handling and retry logic"
    echo "• Enhanced tool descriptions and parallel execution"
    echo "• Comprehensive slash commands system"
    echo "• Auto-completion and input validation"
    echo "• Export/import functionality for sessions"
    echo ""
    echo "🔧 POST-INSTALLATION"
    echo "━━━━━━━━━━━━━━━━━━━━"
    echo "1. Restart your terminal or run: source ~/.bashrc"
    echo "2. Configure your AI provider: arien config"
    echo "3. Start the interactive terminal: arien"
    echo ""
    echo "📚 QUICK START GUIDE"
    echo "━━━━━━━━━━━━━━━━━━━━"
    echo "After installation:"
    echo ""
    echo "1. Setup Deepseek (Cloud AI):"
    echo "   • Get API key from https://platform.deepseek.com/api_keys"
    echo "   • Run: arien config"
    echo "   • Select Deepseek and enter your API key"
    echo ""
    echo "2. Setup Ollama (Local AI):"
    echo "   • Install Ollama from https://ollama.ai"
    echo "   • Pull a model: ollama pull llama2"
    echo "   • Run: arien config"
    echo "   • Select Ollama and configure"
    echo ""
    echo "3. Start using:"
    echo "   • Run: arien"
    echo "   • Type natural language commands"
    echo "   • Use /help for slash commands"
    echo ""
    echo "🆘 TROUBLESHOOTING"
    echo "━━━━━━━━━━━━━━━━━━"
    echo "• Node.js version issues: Use Node Version Manager (nvm)"
    echo "• Permission errors: Check file permissions and PATH"
    echo "• Network issues: Check firewall and proxy settings"
    echo "• API errors: Verify API keys and provider status"
    echo ""
    echo "For more help: https://github.com/arien-ai/arien-ai-cli/issues"
}

# Main execution
main() {
    detect_os

    case "${1:-install}" in
        "install")
            install
            ;;
        "install-local")
            install_local_cmd
            ;;
        "update")
            update
            ;;
        "uninstall")
            uninstall
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "usage")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
